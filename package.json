{"private": true, "scripts": {"dev": "vite", "build": "vite build", "watch": "vite build --watch", "hot": "vite --host"}, "devDependencies": {"@headlessui/vue": "^1.7.16", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "axios": "^1.1.2", "daisyui": "^4.4.19", "laravel-vite-plugin": "^0.7.2", "lodash": "^4.17.19", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^4.0.0"}, "dependencies": {"@phosphor-icons/web": "^2.0.3", "alpinejs": "^3.13.3", "aos": "^2.3.4", "apexcharts": "^3.45.1", "chart.js": "^4.4.1", "gsap": "^3.12.2", "lottie-web": "^5.12.2", "lucide": "^0.294.0", "sweetalert2": "^11.10.1", "swiper": "^11.0.5", "toastify-js": "^1.12.0", "util-deprecate": "^1.0.2"}}